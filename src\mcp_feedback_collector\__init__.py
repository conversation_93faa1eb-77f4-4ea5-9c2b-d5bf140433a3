"""
MCP Feedback Collector - Interactive user feedback collection for MCP servers.

This package provides a modern GUI-based feedback collection system for
Model Context Protocol (MCP) servers, allowing AI assistants to gather
user feedback through an intuitive interface.
"""

__version__ = "2.0.0"
__author__ = "MCP Feedback Collector Team"
__email__ = "<EMAIL>"

from .server import main

__all__ = ["main"] 